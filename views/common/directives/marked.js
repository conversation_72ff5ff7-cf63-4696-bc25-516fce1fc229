import xss from 'xss'
import options from './xssOption'
import MarkdownIt from 'markdown-it';
import mkSanitizer from 'markdown-it-sanitizer'
const md = new MarkdownIt({html: true, linkify: true, typographer: true}).use(mkSanitizer)

const defaultRender = md.renderer.rules.link_open || function (tokens, idx, options, env, self) {
  return self.renderToken(tokens, idx, options);
};

md.renderer.rules.link_open = function (tokens, idx, options, env, self) {
  const token = tokens[idx];
  const aIndex = token.attrIndex('target');
  if (aIndex < 0) {
    token.attrPush(['target', '_blank']);
  } else {
    token.attrs[aIndex][1] = '_blank';
  }
  const relIndex = token.attrIndex('rel');
  if (relIndex < 0) {
    token.attrPush(['rel', 'noopener noreferrer']);
  }
  return defaultRender(tokens, idx, options, env, self);
};
const defaultImgRender = md.renderer.rules.image || function(tokens, idx, options, env, self) {
  return self.renderToken(tokens, idx, options);
};

// 重写 image 渲染规则
md.renderer.rules.image = function (tokens, idx, options, env, self) {
  const token = tokens[idx];
  console.debug('image',self)
  const onclickIndex = token.attrIndex('onclick');
  if (onclickIndex < 0) {
    token.attrPush(['onclick', '__marked_image_click__(this)']);
  } else {
    token.attrs[onclickIndex][1] = '__marked_image_click__(this)';
  }

  return defaultImgRender(tokens, idx, options, env, self);
};
//
//
// const renderer = new marked.Renderer();
//
// renderer.link = function({ href, title, text }) {
//   const titleAttr = title ? ` title="${title}"` : ''
//   return `<a href="${href}" target="_blank" rel="noopener noreferrer"${titleAttr}>${text}</a>`
// }
//
// // renderer.img = function({ href, title, text }) {
// //   const titleAttr = title ? ` title="${title}"` : ''
// //   const altAttr = text ? ` alt="${text}"` : ''
// //   return `<a href="javascript:__marked_image_click__(this)" rel="noopener noreferrer"${titleAttr}><img src="${href}" ${titleAttr}${altAttr}  /></a>`
// // }
//
// renderer.paragraph =function({text }) {
//   return text
// }
// marked.setOptions({
//   renderer
// });
function replaceMarkedImageClick(text, clickFnName) {
  return text.replace(/__marked_image_click__/g, clickFnName);
}
export function safeMarkedHtml(text, clickName) {
  // const html = marked.parse(text)
  const html = md.render(text)
  console.debug('html',replaceMarkedImageClick(html, clickName))
  return xss(replaceMarkedImageClick(html, clickName), options)
}

function setHtml(el, text, clickFnName) {
  el.innerHTML = safeMarkedHtml(text,clickFnName)
}
const count = 0
export default {
  inserted: function (el, binding, vnode) {
    el.__marked_image_click__ = `handleImageClick${count}`
    window[el.__marked_image_click__] = function(imgElement) {
      const event = new CustomEvent('openImage', {
        detail: {
          src: imgElement.src,
          alt: imgElement.alt,
          title: imgElement.title
        },
        bubbles: true
      })
      el.dispatchEvent(event)
    }
    setHtml(el, binding.value, el.__marked_image_click__);
  },
  update: function (el, binding) {
    setHtml(el, binding.value);
  },
  unbind: function (el) {
    window[el.__marked_image_click__] = null
    delete window[el.__marked_image_click__]
    if (el) {
      el.innerHTML = ""
    }
  }
}


