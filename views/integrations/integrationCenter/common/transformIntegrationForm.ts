import { FormElementAnyInputModel, FormElementSelectionModels } from '@model/form/defines/allType'
import { FormElementTransformOption, FormElementType } from '@model/form/defines/shared'
import FormFactoryManager from '@model/form/factory'
import { FormElementSelectionOption } from '@model/form/defines/base'
import moment from 'moment-timezone'
import { FormElementDateModel } from '@model/form/defines/FormDate'
import { BuildFunctionFormViewModel } from '@model/workflow/defines/IntegrationViewModel'
import { FormElementPageModel } from '@model/form/defines/FormPage'
import { FormViewModel } from '@model/form/defines/formViewModel'
import { IntegrationFormType } from '@views/integrations/integrationCenter/common/hooks/useIntegrationForm'

export interface FormIntegrationPageModel extends FormElementPageModel {
  pageGroup?: string
  watch?: string[]
}
const FormMapping = {
  select: FormElementType.DropdownList,
  string: FormElementType.SingleLineText,
  text: FormElementType.MultiLineText,
  number: FormElementType.Number,
  date: FormElementType.Date,
  time: FormElementType.Date,
  'date-time': FormElementType.Date,
  heading: FormElementType.Heading,
  '@moxo.step': FormElementType.DropdownList,
  'multi-select': FormElementType.MultiSelection
}
const IntegrationDateFormat = 'YYYY-MM-DD'

interface FormProps {
  options?: FormElementSelectionOption[];
  style?: string;
  title?: string;
  description?: string;
}

export interface IntegrationFormModel {
  type: string;
  label?: string;
  key?: string;
  tip?: string;
  required?: boolean;
  default?: string;
  props?: FormProps;
  placeholder?: string;
}

export function integrationFormValueToElementFormValue(type, value, model: FormElementAnyInputModel){
  switch (type){
    case IntegrationFormType.DATE:
      const timestamp = moment(value).valueOf()
      return {
        dateStr: value,
        dayTimestamp: timestamp,
        hour: null,
        minute: null,
        timeFormat: 'AM',
        timestamp: timestamp
      }
      break;
    case IntegrationFormType.TIME:
      const parsedTime = moment(value, 'HH:mm:ss');
      const hour = parsedTime.format('hh');
      const minute = parsedTime.format('mm');
      const timeFormat = parsedTime.format('A') as 'AM' | 'PM';
      return {
        dateStr: '',
        dayTimestamp: 0,
        hour,
        minute,
        timeFormat,
        timestamp:parsedTime.valueOf()
      }
      break;
    case IntegrationFormType.DATETIME:
      const parsedDateTime = moment(value);

      const hour = parsedDateTime.format('hh');
      const minute = parsedDateTime.format('mm');
      const timeFormat = parsedDateTime.format('A') as 'AM' | 'PM';
      return  {
        dayTimestamp: 0,
        dateStr: parsedDateTime.format(IntegrationDateFormat),
        hour,
        minute,
        timeFormat,
        timestamp:  parsedDateTime.valueOf()
      }
      break;
    case IntegrationFormType.MOXOSTEP:
      return value?.[IntegrationFormType.MOXOSTEP]?.client_uuid || ''
      break;
    case IntegrationFormType.SELECT:
      //need check the value is in the options
      if(!model.options.find(item => item.value === value)){
        return ''
      }
      return value
      break;
    default:
      return value || ''
      break;
  }
}


export function elementFormValueToIntegrationFormValue(model: FormElementAnyInputModel): string | number | null{
  switch (model.type) {
    case FormElementType.Date:

      const {dateStr,timestamp,hour, minute, timeFormat } = model.value
      let timeStr = void 0;
      if(model.withTime){
        if (hour && minute) {
          const timeMoment = moment({ hour: hour, minute: minute });
          if (timeFormat === 'PM') {
            timeMoment.add(12, 'hours');
          }
          timeStr = timeMoment.format('HH:mm:ss')
        }
      }
      if(model.subType === IntegrationFormType.TIME) {
        return timeStr
      }else if(model.subType === IntegrationFormType.DATE){
        return dateStr
      }else if(model.subType === IntegrationFormType.DATETIME){
        return moment(timestamp).utc().format('YYYY-MM-DDTHH:mm:ss[Z]')
      }
      return void 0
      break;
  }
  if(model.subType === IntegrationFormType.MOXOSTEP){
    return {
      [IntegrationFormType.MOXOSTEP]:{
        client_uuid: model.value
      }
    }
  }
  if(!model.value){
    return ''
  }
  return String(model.value)
}

export function integrationFormToPageViewModel (
  integrationForm: BuildFunctionFormViewModel,
  option: FormElementTransformOption
): FormElementPageModel {
  const pageViewModel = FormFactoryManager.createFormElementViewModel(FormElementType.Page,
    option
  ) as FormIntegrationPageModel
  const formOption = integrationForm.output.options || {}
  pageViewModel.pageGroup = formOption.page_group || ''
  pageViewModel.watch = formOption.watch || []

  const formFilledValues = option.formDefaultValues || {}
  integrationForm.output.form.forEach((item) => {
    const { type, key, label, tip, placeholder, required, default: defValue, props: defProps,hidden } = item
    let defaultValue = defValue
    const props = defProps || {}
    if (FormMapping[type]) {
      const elementType = FormMapping[type]
      const elementModel = FormFactoryManager.createFormElementViewModel(
        elementType,
        option
      ) as FormElementAnyInputModel
      elementModel.subType = type
      elementModel.label = label
      elementModel.id = key
      elementModel.placeholder = placeholder
      elementModel.supporting = tip
      elementModel.required = required
      elementModel.readonly = false
      elementModel.isVisible = !(hidden===true)

      if(elementType === FormElementType.DropdownList){
        if(Array.isArray(defaultValue)){
          defaultValue = defaultValue[0]
        }
        /**
         * hardcode for moxo step type
         */
        if(type === IntegrationFormType.MOXOSTEP){
          props.options = option.steps?.map(step => ({ label: step.title, value: step.clientUuid }))
        }
      }


      if (elementType === FormElementType.Date) {
        const model = elementModel as FormElementDateModel
        switch (type) {
          case 'date':
            model.withTime = false
            model.withDate = true
            model.dateFormat = IntegrationDateFormat
            break;
          case 'time':
            model.withDate = false
            model.withTime = true
            break;
          case 'date-time':
            model.withTime = true
            model.withDate = true
            model.dateFormat = IntegrationDateFormat
            break;
        }
      }else if(elementType === FormElementType.Heading) {
        elementModel.label = props?.title
        elementModel.supporting = props?.description
      }

      if ([FormElementType.DropdownList,
          FormElementType.MultiSelection,
          FormElementType.SingleSelection].includes(elementType)) {
        (elementModel as FormElementSelectionModels).options = props?.options || []
      }
      const value = integrationFormValueToElementFormValue(type, formFilledValues[key] || defaultValue, elementModel)
      elementModel.value = value

      pageViewModel.elements.push(elementModel)
    }
  })
  return pageViewModel
}

export function getIntegrationFormValue(formViewModel:FormViewModel){
  const formData = {}
  const formArray = []
  FormFactoryManager.process(formViewModel, (element) =>{
    if(element.type === FormElementType.Heading){
      // heading is readonly not need submit to server
      return
    }
    const value = elementFormValueToIntegrationFormValue(element)
    formData[element.id] = value
    formArray.push({
      key: element.id,
      label: element.label,
      value: value
    })
  })
  return {
    formData, formArray
  }
}

export interface IntegrationFormOption  extends Partial<FormElementTransformOption>{
  steps: any[];
  roles: any[];
  actionKey?: string;
  enableDDR?: boolean;
  appId: string;
  authId: string;
  assignees: any[];
  title: string;
  description: string;
  /**
   * for edit case
   */
  formDefaultValues: Record<string, string | number | string[]>;
}
export interface IntegrationFillFormParams{
  board_id: string;
  button_id: string;
  input: any;
  button_id: string;
  step_sequence: number;
  transaction_sequence: number;

}
export interface IntegrationFillFormOption  extends Partial<FormElementTransformOption>{
  baseParams: IntegrationFillFormParams;
}
export enum IntegrationFormActionType{
  BuildForm,
  FillForm
}

export function transformIntegrationAssignees(assignee){
  let user
  if (assignee.isRole) {
    //is role
    user =  {
      role: {
        id: assignee.userId || assignee.id,
      },
    }
  } else {
    //is user
    const {phoneNumber, id, name, email} = assignee
    user = {
      user: {
        id,
        name,
        email,
        phone_number:phoneNumber
      },
    }
  }
  return [user]
}