<script lang="ts">
import { defineComponent, inject } from '@vue/composition-api'
import ContentWrapper from '@views/integrations/integrationCenter/components/ContentWrapper.vue'
import FormCanvasView from '@views/form/components/FormCanvasView.vue'
import FormTheme from '@views/form/components/FormTheme.vue'
import { PropType } from 'vue'
import { FormElementType, FormRuntimeOptions, FormScenes } from '@model/form/defines/shared'
import FormFactoryManager from '@model/form/factory'
import { FormElementEventParams, FormEventCallback } from '@views/form/common/types'
import { useIntegrationCenterStore } from '@views/stores/integrationCenter'
import { mapActions } from 'pinia'
import { installFormComponents } from '@views/form/install'
import { FormViewModel } from '@model/form/defines/formViewModel'
import { IntegrationDefines } from '@commonUtils'
import { BuildFunctionActionViewModel, BuildFunctionFormViewModel } from '@model/workflow/defines/IntegrationViewModel'
import {
  FormIntegrationPageModel,
  getIntegrationFormValue, IntegrationFillFormOption,
  IntegrationFormActionType,
  IntegrationFormOption,
  integrationFormToPageViewModel
} from '@views/integrations/integrationCenter/common/transformIntegrationForm'
import { CCustomError } from '@controller/common/CCustomError'
import ErrorView from '../../../../app/tinyProj/forms/src/views/integrationForm/ErrorView.vue'
import AmIntegrationPreview from '@views/automationBuilder/components/integration/components/AmIntegrationPreview.vue'
import cloneDeep from 'lodash/cloneDeep'

export default defineComponent({
  name: 'IntegrationFormView',
  components: { AmIntegrationPreview, ErrorView, FormTheme, FormCanvasView, ContentWrapper },
  props: {
    /**
     * this option is for buildForm case
     */
    integrationOptions: {
      type: Object as PropType<IntegrationFormOption>,
      default: () => ({})
    },
    /**
     * this option is for FillForm case
     */
    fillFormOptions:{
      type: Object as PropType<IntegrationFillFormOption>,
      default: () => ({})
    },
    actionType: {
      type: Number as PropType<IntegrationFormActionType>,
      default: IntegrationFormActionType.BuildForm
    },
    hideBackButton: {
      type: Boolean,
      default: false
    },
    useButtonLoading: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const runtimeOptions: FormRuntimeOptions = {
      isTemplate: false,
      enableDDR: this.integrationOptions.enableDDR,
      showProtected: true,
      scenes: FormScenes.FillForm
    }
    /**
     * lastValueCache is for save user filled value between page change
     */
    const lastValueCache = {}
    const formViewModel = FormFactoryManager.createFormElementViewModel(FormElementType.Form, {
      $t: this.$t
    }) as FormViewModel
    const currentPage = FormFactoryManager.createFormElementViewModel(FormElementType.Page, {
      $t: this.$t
    }) as FormIntegrationPageModel
    return {
      runtimeOptions,
      formViewModel,
      loading: true,
      currentPage,
      lastValueCache
    }
  },

  beforeCreate() {
    installFormComponents()
  },
  setup(props) {
    const integrationApp = inject('integrationApp', {})
    const basicBoardInfo = inject('basicBoardInfo', {})

    const buildFunctionModel = (formData): IntegrationDefines.BuildFunctionModel => {
      const { appId, authId, actionKey, assignees, title, description } = props.integrationOptions
      return {
        app_id: appId,
        auth_id: authId,
        input: {
          ...formData
        },
        context: {
          action_key: actionKey,
          title,
          description,
          assignees
        }
      }
    }
    return {
      isLastFormPage: false,
      integrationApp,
      basicBoardInfo,
      buildFunctionModel,
      errorInfo: null,
      previewData: {},
      showPreview: false
    }
  },
  computed: {
    isFirstPage() {
      if (this.formViewModel.pages.length <= 1) {
        return true
      }
      const pageGroup = this.currentPage?.pageGroup
      return !!pageGroup && pageGroup === this.formViewModel.pages?.[0]?.pageGroup
    }
  },
  mounted() {
    this.callIntegrationServer({})
  },
  methods: {
    ...mapActions(useIntegrationCenterStore, ['invokeBuildFunction','invokeActionFunction']),
    getElementRenderComponent(element) {
      return FormFactoryManager.uiOption(element, this.runtimeOptions).view
    },
    invokeFunction(formData){
      if(this.actionType === IntegrationFormActionType.BuildForm){
        const { appId, authId, actionKey, assignees, title, description } = this.integrationOptions
        const params = {
          app_id: appId,
          auth_id: authId,
          input: {
            ...formData
          },
          context: {
            action_key: actionKey,
            title,
            description,
            assignees
          }
        } as IntegrationDefines.BuildFunctionModel
        return this.invokeBuildFunction(params)
      }else{
        const base = cloneDeep(this.fillFormOptions)
        const baseInput = base.input || {}
        const params =  {
          ...base,
          input: {
            ...baseInput,
            ...formData
          }
        } as IntegrationDefines.ActionFunctionModel
        return this.invokeActionFunction(params)
      }
    },
    addNewFormPage(page: FormIntegrationPageModel) {
      this.formViewModel.pages.push(page)
      const isFirstPage = this.currentPage.elements.length <= 0
      const oldVersion = !this.currentPage.pageGroup && !page.pageGroup
      const isDifferentGroup = this.currentPage.pageGroup !== page.pageGroup || oldVersion
      const hasWatch = Array.isArray(page.watch) && page.watch.length
      if (isFirstPage || isDifferentGroup) {
        this.currentPage.elements = Array.from(page.elements)
        this.currentPage.pageGroup = page.pageGroup
        this.currentPage.watch = page.watch
        this.currentPage.id = page.id
      } else {
        page.elements.forEach((element) => {
          this.currentPage.elements.push(element)
        })
      }
    },
    async callIntegrationServer(formData) {
      this.loading = true

      try {
        const option = {
          $t: this.$t,
          boardId: '',
          transactionSequence: '',
          ...this.integrationOptions
        }
        const invokedData = (await this.invokeFunction(formData)) as
          | BuildFunctionFormViewModel
          | BuildFunctionActionViewModel
        if (invokedData.error) {
          //error message given by third-party app api
          const errBody = invokedData.error || {}
          const errorInfo = {
            isThirdPartyError: true,
            title: errBody.title || this.$t('something_went_wrong_tip'),
            subtitle:
              errBody.description ||
              this.$t('integration_third_party_error_default', {
                appName: this.integrationApp.app_name
              })
          }
          throw new CCustomError('', errorInfo)
        }

        if (invokedData.output?.form) {
          const pageModel = integrationFormToPageViewModel(
            invokedData as BuildFunctionFormViewModel,
            option as IntegrationFormOption
          )
          this.addNewFormPage(pageModel)
        } else {
          const { formData, formArray } = getIntegrationFormValue(this.formViewModel)

          if (this.actionType === IntegrationFormActionType.FillForm) {
            if(invokedData.output?.events){
              this.$emit('events', invokedData.output.events)
            }
            this.$emit('success', { formData, formArray, invokedData: invokedData.output })
            this.isLastFormPage = true
            return
          }
          let isEnded = false
          if (invokedData.output.action || invokedData.output.await) {
            //return action without form list
            isEnded = true
          } else if (invokedData.output?.automation) {
            this.previewData.invokedData = invokedData.output
            /**
             * Delegate control to the external logic and display the externally inserted button.
             */
            this.showPreview = true
          } else if (invokedData.output?.variables) {
            //for trigger
            isEnded = true
          }
          if (isEnded || invokedData.output?.no_more_forms || invokedData.output?.automation) {
            this.$emit('success', { formData, formArray, invokedData: invokedData.output })
          }
        }
      } catch (error) {
        if (error.isThirdPartyError) {
          this.errorInfo = error
        } else {
          this.errorInfo = {
            title: this.$t('Something_Went_Wrong_title'),
            subtitle: this.$t('integration_integration_server_error')
          }
        }
      } finally {
        this.loading = false
      }
    },
    handleElementBlur(eventInfo: FormElementEventParams, resultCallback: FormEventCallback) {
      const { element } = eventInfo
      let changeElementInPage = -1
      let needSubmit = false
      const removedElementIds = []
      this.lastValueCache[element.id] = element.value

      FormFactoryManager.process(this.formViewModel, (elementModel, factory, page, pageIndex) => {
        if (elementModel.id === element.id) {
          factory.setValue(elementModel, eventInfo.currentValue, {
            changedProperty: eventInfo.changedProperty
          })
          factory.validate(elementModel, { notSaveError: true })
          if ((page as FormIntegrationPageModel).watch?.includes(elementModel.id)) {
            changeElementInPage = pageIndex
            needSubmit = true
          }
        }
      })
      if (changeElementInPage >= 0 && changeElementInPage < this.formViewModel.pages.length) {
        // Delete all pages from changeElementInPage onwards
        this.formViewModel.pages.splice(changeElementInPage + 1)
        const elements = []
        FormFactoryManager.process(this.formViewModel, (elementModel) => {
          elements.push(elementModel)
        })
        this.currentPage.elements = elements
      }

      if (needSubmit) {
        this.doSubmit(changeElementInPage, true)
      }
      console.debug('blur', eventInfo)
    },
    handleElementChange(eventInfo: FormElementEventParams) {
      console.debug('change', eventInfo)
    },
    doSubmit(changeElementInPage?: number, isAutoSubmit?: boolean) {
      FormFactoryManager.validate(this.formViewModel, {
        enableDDR: this.enableDDR,
        validatePage: changeElementInPage,
        notSaveError: isAutoSubmit
      })
        .then(() => {
          const { formData } = getIntegrationFormValue(this.formViewModel)
          this.callIntegrationServer(formData)
        })
        .catch((err) => {
          this.error = err
          if(!isAutoSubmit) {
            this.$mxMessage.error(this.$t('please_ensure_all_errors_are_resolved_before_continuing'))
            this.scrollToErrorField()
          }
        })
    },
    scrollToErrorField() {
      const erritem = this.$el.querySelector('.is-field-error')
      erritem?.scrollIntoView({
        block: 'center',
        behavior: 'smooth'
      })
    },
    gotoNext() {
      this.doSubmit()
    },
    handleRetry() {
      this.errorInfo = null
      this.doSubmit()
    },
    exitPreview() {
      this.isLastFormPage = false
      this.showPreview = false
    },
    goBack() {
      this.isLastFormPage = false
      /**
       * remove current page
       */
      if (this.formViewModel.pages.length <= 1) {
        // notify outer current is not prev page
        return false
      }
      const lastPage = this.formViewModel.pages.pop()
      const pageCount = this.formViewModel.pages.length
      const isFirstPage = pageCount === 1
      const elements = []
      let pages = []
      if (!isFirstPage) {
        for (let i = this.formViewModel.pages.length - 1; i >= 0; i--) {
          const page = this.formViewModel.pages[i]
          const oldVersion = !lastPage.pageGroup && !page.pageGroup
          const isSameGroup = lastPage.pageGroup === page.pageGroup && !oldVersion
          if (isSameGroup) {
            this.formViewModel.pages.splice(i)
          } else {
            break
          }
        }
        if (this.formViewModel.pages.length > 1) {
          const theLastPage = this.formViewModel.pages[this.formViewModel.pages.length - 1]
          pages = this.formViewModel.pages.filter((page) => {
            return page.pageGroup === theLastPage.pageGroup
          })
        } else {
          pages = this.formViewModel.pages
        }
      } else {
        pages = this.formViewModel.pages
      }
      let theLastPage
      pages.forEach((page) => {
        if (!theLastPage) {
          theLastPage = page
        }
        page.elements.forEach((element) => {
          elements.push(element)
        })
      })

      const option = {
        $t: this.$t,
        boardId: '',
        transactionSequence: ''
      }

      const pageViewModel = FormFactoryManager.createFormElementViewModel(
        FormElementType.Page,
        option
      ) as FormIntegrationPageModel
      pageViewModel.elements = elements
      pageViewModel.pageGroup = theLastPage.pageGroup
      pageViewModel.watch = theLastPage.watch
      pageViewModel.id = theLastPage.id
      this.currentPage = pageViewModel
      return true
    }
  }
})
</script>

<template>
  <div
    class="integration-fill-form"
    v-loading="loading"
    element-class="integration-fill-form-loading"
    element-loading-background="rgba(255, 255, 255, 0.4)"
  >
    <div class="integration-fill-form-wrap">
      <div class="preview-integration-form" v-if="showPreview">
        <div class="go-back-nav-icon mx-text-c1 mx-clickable" @click="exitPreview">
          <i class="micon-arrow-left-sf" />
          <span>{{ $t('back') }}</span>
        </div>
        <AmIntegrationPreview
          :actionData="integrationOptions.actionData"
          :invokedData="previewData.invokedData"
          class="integration-preview"
        />
      </div>
      <template v-if="!errorInfo && !showPreview">
        <el-form class="integration-form-body" @submit.native.prevent>
          <FormTheme :scale="1" :runtime-options="runtimeOptions">
            <FormElementView
              v-for="element in currentPage.elements"
              :key="element.id"
              :element="element"
              :runtime-options="runtimeOptions"
              :pageWidth="currentPage.width"
              :scale="1"
            >
              <component
                :is="getElementRenderComponent(element)"
                :key="element.id + '-comp'"
                :marked="true"
                :element="element"
                :scale="1"
                :runtime-options="runtimeOptions"
                @change="handleElementChange"
                @blur="handleElementBlur"
              />
            </FormElementView>
          </FormTheme>
        </el-form>
        <div class="integration-form-action">
          <el-button
            v-if="!isFirstPage && !hideBackButton"
            :disabled="loading"
            type="gray-branding"
            @click="goBack"
            >{{ $t('back') }}
          </el-button>
          <ElButton
            v-if="!isLastFormPage"
            type="primary"
            :debounce-click="300"
            :data-ta="'next_btn'"
            size="medium"
            :title="$t('next')"
            :disabled="loading"
            @button-click="gotoNext"
          >
            {{ $t('next') }}
          </ElButton>
        </div>
      </template>
      <div v-if="errorInfo" class="integration-form-error">
        <ErrorView :error-body="errorInfo" :show-retry="!errorInfo.isThirdPartyError" @retry="handleRetry" />
      </div>
    </div>
    <div v-if="isLastFormPage" class="integration-form-action for-outside">
      <slot></slot>
    </div>
  </div>
</template>

<style scoped lang="scss">
.integration-fill-form {
  display: flex;
  height: 100%;
  border-radius: 6px;
  width: 100%;
  flex-direction: column;
  overflow: hidden;
  flex-grow: 1;
}

.integration-form-error {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  min-height: 150px;
  font-size: 16px;
}

.go-back-nav-icon {
  margin: 23px 28px;
}

.for-outside {
  background: #fff;
  border-bottom-left-radius: 6px;
  border-bottom-right-radius: 6px;
}

.integration-fill-form-wrap {
  height: 100%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
}

.integration-form-body {
  height: 100%;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  overflow-y: scroll;

  ::v-deep {
    .form-element {
      padding: 5px 28px;
    }
  }

  padding: 10px 0 20px;
}

.integration-form-action {
  height: 56px;
  padding: 10px 28px;
  flex-shrink: 0;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #e0e0e0;
}

.integration-preview {
  margin: 28px;
}

.integration-form-action {
  display: flex;
  gap: 8px;
}

//.integration-form-action > button {
//  flex: 1 1 auto;
//}
//
//.integration-form-action > button:only-child {
//  width: 100%;
//}
</style>
